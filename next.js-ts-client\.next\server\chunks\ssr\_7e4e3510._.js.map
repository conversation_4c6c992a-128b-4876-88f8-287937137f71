{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/(shop)/(checkout-process)/checkout/address-choice/AddressStage.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"address_selection\": \"AddressStage-module-scss-module__cybDRG__address_selection\",\n  \"address_stage\": \"AddressStage-module-scss-module__cybDRG__address_stage\",\n  \"cart\": \"AddressStage-module-scss-module__cybDRG__cart\",\n  \"contact_details\": \"AddressStage-module-scss-module__cybDRG__contact_details\",\n  \"missing_addresses\": \"AddressStage-module-scss-module__cybDRG__missing_addresses\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/utils/empty-cart/EmptyCart.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"action_button\": \"EmptyCart-module-scss-module__p5kxma__action_button\",\n  \"action_section\": \"EmptyCart-module-scss-module__p5kxma__action_section\",\n  \"content_section\": \"EmptyCart-module-scss-module__p5kxma__content_section\",\n  \"description\": \"EmptyCart-module-scss-module__p5kxma__description\",\n  \"empty_cart\": \"EmptyCart-module-scss-module__p5kxma__empty_cart\",\n  \"heading\": \"EmptyCart-module-scss-module__p5kxma__heading\",\n  \"icon_section\": \"EmptyCart-module-scss-module__p5kxma__icon_section\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/empty-cart/EmptyCart.tsx"], "sourcesContent": ["import Link from 'next/link'\r\nimport { FaShoppingCart } from 'react-icons/fa'\r\nimport styles from './EmptyCart.module.scss'\r\n\r\ninterface Props {\r\n  message?: string\r\n  showIcon?: boolean\r\n  actionText?: string\r\n  actionHref?: string\r\n}\r\n\r\nconst EmptyCart = ({\r\n  message,\r\n  showIcon = true,\r\n  actionText = \"Go Shopping\",\r\n  actionHref = \"/\"\r\n}: Props) => {\r\n  return (\r\n    <div className={styles.empty_cart} role=\"region\" aria-label=\"Empty cart\">\r\n      {showIcon && (\r\n        <div className={styles.icon_section} aria-hidden=\"true\">\r\n          <FaShoppingCart />\r\n        </div>\r\n      )}\r\n\r\n      <div className={styles.content_section}>\r\n        <h2 className={styles.heading}>\r\n          {message ? message : \"Your cart is empty\"}\r\n        </h2>\r\n        <p className={styles.description}>\r\n          Add some products to your cart to get started with your shopping journey.\r\n        </p>\r\n      </div>\r\n\r\n      <div className={styles.action_section}>\r\n        <Link\r\n          href={actionHref}\r\n          className={styles.action_button}\r\n          aria-label={`${actionText} - Browse products to add to your cart`}\r\n        >\r\n          {actionText}\r\n        </Link>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\nexport default EmptyCart"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AASA,MAAM,YAAY,CAAC,EACjB,OAAO,EACP,WAAW,IAAI,EACf,aAAa,aAAa,EAC1B,aAAa,GAAG,EACV;IACN,qBACE,8OAAC;QAAI,WAAW,gLAAM,CAAC,UAAU;QAAE,MAAK;QAAS,cAAW;;YACzD,0BACC,8OAAC;gBAAI,WAAW,gLAAM,CAAC,YAAY;gBAAE,eAAY;0BAC/C,cAAA,8OAAC,gKAAc;;;;;;;;;;0BAInB,8OAAC;gBAAI,WAAW,gLAAM,CAAC,eAAe;;kCACpC,8OAAC;wBAAG,WAAW,gLAAM,CAAC,OAAO;kCAC1B,UAAU,UAAU;;;;;;kCAEvB,8OAAC;wBAAE,WAAW,gLAAM,CAAC,WAAW;kCAAE;;;;;;;;;;;;0BAKpC,8OAAC;gBAAI,WAAW,gLAAM,CAAC,cAAc;0BACnC,cAAA,8OAAC,uKAAI;oBACH,MAAM;oBACN,WAAW,gLAAM,CAAC,aAAa;oBAC/B,cAAY,GAAG,WAAW,sCAAsC,CAAC;8BAEhE;;;;;;;;;;;;;;;;;AAKX;uCACe", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/utils/underlay/Underlay.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"underlay\": \"Underlay-module-scss-module__PkWa8a__underlay\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/underlay/Underlay.tsx"], "sourcesContent": ["import React from 'react'\r\nimport styles from './Underlay.module.scss'\r\n\r\ninterface Props {\r\n  children: React.ReactNode\r\n  isOpen: boolean\r\n  onClose?: () => void\r\n  bgOpacity?: number\r\n  // zIndex?: number\r\n}\r\n\r\nconst Underlay = ({ children, isOpen, onClose, bgOpacity = 0.3 }: Props) => {\r\n\r\n  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {\r\n    if (e.target === e.currentTarget && onClose) {\r\n      onClose()\r\n    }\r\n  }\r\n\r\n  return isOpen ? (\r\n    <div\r\n      className={styles.underlay}\r\n      style={{ backgroundColor: `rgba(0, 0, 0, ${bgOpacity})` }}\r\n      onClick={handleOverlayClick}\r\n    >\r\n      {children}\r\n    </div>\r\n  ) : null\r\n}\r\n\r\nexport default Underlay"], "names": [], "mappings": ";;;;;AACA;;;AAUA,MAAM,WAAW,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,GAAG,EAAS;IAErE,MAAM,qBAAqB,CAAC;QAC1B,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,IAAI,SAAS;YAC3C;QACF;IACF;IAEA,OAAO,uBACL,8OAAC;QACC,WAAW,0KAAM,CAAC,QAAQ;QAC1B,OAAO;YAAE,iBAAiB,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QAAC;QACxD,SAAS;kBAER;;;;;mDAED;AACN;uCAEe", "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/spinner/Spinner.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport SyncLoader from 'react-spinners/SyncLoader'\r\nimport <PERSON><PERSON><PERSON>oader from 'react-spinners/ClipLoader'\r\nimport PulseLoader from 'react-spinners/PulseLoader'\r\nimport RiseLoader from 'react-spinners/RiseLoader'\r\nimport RotateLoader from 'react-spinners/RotateLoader'\r\nimport ScaleLoader from 'react-spinners/ScaleLoader'\r\nimport CircleLoader from 'react-spinners/CircleLoader'\r\nimport Underlay from '../underlay/Underlay'\r\n\r\ninterface Props {\r\n  loading: boolean\r\n  color?: string\r\n  size?: number\r\n  thickness?: number\r\n  bgOpacity?: number\r\n  useUnderlay?: boolean\r\n  spinnerType?: 'sync' | 'pulse' | 'clip' | 'rise' | 'rotate' | 'scale' | 'circle'\r\n}\r\n\r\nconst Spinner = ({\r\n  loading,\r\n  color,\r\n  size = 150,\r\n  thickness = 5,\r\n  bgOpacity,\r\n  useUnderlay = false,\r\n  spinnerType = 'sync'\r\n}: Props) => {\r\n  const renderSpinner = () => {\r\n    const commonProps = {\r\n      color,\r\n      loading,\r\n      size,\r\n      thickness,\r\n      'aria-label': 'Loading Spinner',\r\n    }\r\n\r\n    switch (spinnerType) {\r\n      case 'clip':\r\n        return <ClipLoader {...commonProps} />\r\n      case 'pulse':\r\n        return <PulseLoader {...commonProps} />\r\n      case 'rise':\r\n        return <RiseLoader {...commonProps} />\r\n      case 'rotate':\r\n        return <RotateLoader {...commonProps} />\r\n      case 'scale':\r\n        return <ScaleLoader {...commonProps} />\r\n      case 'circle':\r\n        return <CircleLoader {...commonProps} />\r\n      case 'sync':\r\n      default:\r\n        return <SyncLoader {...commonProps} />\r\n    }\r\n  }\r\n\r\n  if (!useUnderlay) {\r\n    return loading ? renderSpinner() : null\r\n  }\r\n\r\n  return (\r\n    <Underlay isOpen={loading} bgOpacity={bgOpacity}>\r\n      {renderSpinner()}\r\n    </Underlay>\r\n  )\r\n}\r\n\r\nexport default Spinner"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAqBA,MAAM,UAAU,CAAC,EACf,OAAO,EACP,KAAK,EACL,OAAO,GAAG,EACV,YAAY,CAAC,EACb,SAAS,EACT,cAAc,KAAK,EACnB,cAAc,MAAM,EACd;IACN,MAAM,gBAAgB;QACpB,MAAM,cAAc;YAClB;YACA;YACA;YACA;YACA,cAAc;QAChB;QAEA,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,0JAAU;oBAAE,GAAG,WAAW;;;;;;YACpC,KAAK;gBACH,qBAAO,8OAAC,2JAAW;oBAAE,GAAG,WAAW;;;;;;YACrC,KAAK;gBACH,qBAAO,8OAAC,0JAAU;oBAAE,GAAG,WAAW;;;;;;YACpC,KAAK;gBACH,qBAAO,8OAAC,4JAAY;oBAAE,GAAG,WAAW;;;;;;YACtC,KAAK;gBACH,qBAAO,8OAAC,2JAAW;oBAAE,GAAG,WAAW;;;;;;YACrC,KAAK;gBACH,qBAAO,8OAAC,4JAAY;oBAAE,GAAG,WAAW;;;;;;YACtC,KAAK;YACL;gBACE,qBAAO,8OAAC,0JAAU;oBAAE,GAAG,WAAW;;;;;;QACtC;IACF;IAEA,IAAI,CAAC,aAAa;QAChB,OAAO,UAAU,kBAAkB;IACrC;IAEA,qBACE,8OAAC,8JAAQ;QAAC,QAAQ;QAAS,WAAW;kBACnC;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/TextLimit.tsx"], "sourcesContent": ["interface Props {\r\n  title: string\r\n  maxLength: number\r\n}\r\n\r\nconst LimitTitleLength = ({ title, maxLength }: Props) => {\r\n  function limitTitleLength(title: string, maxLength: number) {\r\n    if (title.length > maxLength) {\r\n      return title.substring(0, maxLength) + '...'\r\n    }\r\n    return title\r\n  }\r\n\r\n  return <>{limitTitleLength(title, maxLength)}</>\r\n}\r\n\r\nexport default LimitTitleLength"], "names": [], "mappings": ";;;;;;AAKA,MAAM,mBAAmB,CAAC,EAAE,KAAK,EAAE,SAAS,EAAS;IACnD,SAAS,iBAAiB,KAAa,EAAE,SAAiB;QACxD,IAAI,MAAM,MAAM,GAAG,WAAW;YAC5B,OAAO,MAAM,SAAS,CAAC,GAAG,aAAa;QACzC;QACA,OAAO;IACT;IAEA,qBAAO;kBAAG,iBAAiB,OAAO;;AACpC;uCAEe", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cart_item\": \"CartItemsList-module-scss-module__kultEW__cart_item\",\n  \"cart_item__extra_data\": \"CartItemsList-module-scss-module__kultEW__cart_item__extra_data\",\n  \"cart_item__img\": \"CartItemsList-module-scss-module__kultEW__cart_item__img\",\n  \"cart_item__info\": \"CartItemsList-module-scss-module__kultEW__cart_item__info\",\n  \"cart_item__quantity\": \"CartItemsList-module-scss-module__kultEW__cart_item__quantity\",\n  \"cart_item__title\": \"CartItemsList-module-scss-module__kultEW__cart_item__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/%28shop%29/%28checkout-process%29/components/cart/CartItemsList.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { <PERSON><PERSON><PERSON>, FiMinus, FiTrash2 } from 'react-icons/fi'\r\nimport Link from 'next/link'\r\nimport LimitTitleLength from '@/src/components/utils/TextLimit'\r\nimport { CartItemShape } from '@/src/types/store-types'\r\nimport styles from './CartItemsList.module.scss'\r\n\r\ninterface CartItemsListProps {\r\n  cartItems: CartItemShape[]\r\n  handleIncrement?: (item: CartItemShape) => void\r\n  handleDecrement?: (item: CartItemShape) => void\r\n  deleteCartItem?: (itemId: number) => void\r\n}\r\n\r\nconst CartItemsList = ({\r\n  cartItems,\r\n  handleIncrement,\r\n  handleDecrement,\r\n  deleteCartItem,\r\n}: CartItemsListProps) => {\r\n  return (\r\n    <ul>\r\n      {cartItems?.map((item: CartItemShape) => (\r\n        <li key={item.id} className={styles.cart_item}>\r\n          <div className={styles.cart_item__img}>\r\n            <img\r\n              src={\r\n                item.product_variant?.product_image?.[0]?.image\r\n                  ? `${process.env.NEXT_PUBLIC_CLOUDINARY_URL}/${item.product_variant.product_image[0].image}`\r\n                  : noImagePlaceholder\r\n              }\r\n              alt={\r\n                item.product_variant?.product_image?.[0]?.alternative_text ||\r\n                item.product.title\r\n              }\r\n            />\r\n          </div>\r\n          <div className={styles.cart_item__info}>\r\n            <span className={styles.cart_item__title}>\r\n              <Link href={`/product/${item.product.slug}/`}>\r\n                <LimitTitleLength title={item.product.title} maxLength={60} />\r\n              </Link>\r\n            </span>\r\n            {` `}\r\n            <span>\r\n              (${item.product_variant.price} x {item.quantity})\r\n            </span>\r\n            <span>\r\n              Variant: {item.product_variant?.price_label?.attribute_value}\r\n            </span>\r\n            {Object.entries(item.extra_data).map(([key, value], index) => (\r\n              <div key={index} className={styles.cart_item__extra_data}>\r\n                <p>{key} :</p>\r\n                <p>{value}</p>\r\n              </div>\r\n            ))}\r\n          </div>\r\n          <div className={styles.cart_item__quantity}>\r\n            <div>\r\n              <p>Qty:</p>\r\n              {handleDecrement && (\r\n                <button\r\n                  onClick={() => handleDecrement(item)}\r\n                  disabled={item.product_variant.stock_qty === 0}\r\n                >\r\n                  <i>\r\n                    <FiMinus />\r\n                  </i>\r\n                </button>\r\n              )}\r\n              <p>{item.quantity}</p>\r\n              {handleIncrement && (\r\n                <button\r\n                  onClick={() => handleIncrement(item)}\r\n                  disabled={item.product_variant.stock_qty === 0}\r\n                >\r\n                  <i>\r\n                    <FiPlus />\r\n                  </i>\r\n                </button>\r\n              )}\r\n              {deleteCartItem && (\r\n                <button onClick={() => deleteCartItem(item.id)}>\r\n                  <i>\r\n                    <FiTrash2 />\r\n                  </i>\r\n                </button>\r\n              )}\r\n            </div>\r\n            {item.product_variant.stock_qty === 0 && <p>Out of Stock</p>}\r\n          </div>\r\n        </li>\r\n      ))}\r\n    </ul>\r\n  )\r\n}\r\n\r\nexport default CartItemsList\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAeA,MAAM,gBAAgB,CAAC,EACrB,SAAS,EACT,eAAe,EACf,eAAe,EACf,cAAc,EACK;IACnB,qBACE,8OAAC;kBACE,WAAW,IAAI,CAAC,qBACf,8OAAC;gBAAiB,WAAW,yMAAM,CAAC,SAAS;;kCAC3C,8OAAC;wBAAI,WAAW,yMAAM,CAAC,cAAc;kCACnC,cAAA,8OAAC;4BACC,KACE,KAAK,eAAe,EAAE,eAAe,CAAC,EAAE,EAAE,QACtC,yEAA0C,CAAC,EAAE,KAAK,eAAe,CAAC,aAAa,CAAC,EAAE,CAAC,KAAK,EAAE,GAC1F;4BAEN,KACE,KAAK,eAAe,EAAE,eAAe,CAAC,EAAE,EAAE,oBAC1C,KAAK,OAAO,CAAC,KAAK;;;;;;;;;;;kCAIxB,8OAAC;wBAAI,WAAW,yMAAM,CAAC,eAAe;;0CACpC,8OAAC;gCAAK,WAAW,yMAAM,CAAC,gBAAgB;0CACtC,cAAA,8OAAC,uKAAI;oCAAC,MAAM,CAAC,SAAS,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;8CAC1C,cAAA,8OAAC,mJAAgB;wCAAC,OAAO,KAAK,OAAO,CAAC,KAAK;wCAAE,WAAW;;;;;;;;;;;;;;;;4BAG3D,CAAC,CAAC,CAAC;0CACJ,8OAAC;;oCAAK;oCACD,KAAK,eAAe,CAAC,KAAK;oCAAC;oCAAI,KAAK,QAAQ;oCAAC;;;;;;;0CAElD,8OAAC;;oCAAK;oCACM,KAAK,eAAe,EAAE,aAAa;;;;;;;4BAE9C,OAAO,OAAO,CAAC,KAAK,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE,sBAClD,8OAAC;oCAAgB,WAAW,yMAAM,CAAC,qBAAqB;;sDACtD,8OAAC;;gDAAG;gDAAI;;;;;;;sDACR,8OAAC;sDAAG;;;;;;;mCAFI;;;;;;;;;;;kCAMd,8OAAC;wBAAI,WAAW,yMAAM,CAAC,mBAAmB;;0CACxC,8OAAC;;kDACC,8OAAC;kDAAE;;;;;;oCACF,iCACC,8OAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,UAAU,KAAK,eAAe,CAAC,SAAS,KAAK;kDAE7C,cAAA,8OAAC;sDACC,cAAA,8OAAC,yJAAO;;;;;;;;;;;;;;;kDAId,8OAAC;kDAAG,KAAK,QAAQ;;;;;;oCAChB,iCACC,8OAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,UAAU,KAAK,eAAe,CAAC,SAAS,KAAK;kDAE7C,cAAA,8OAAC;sDACC,cAAA,8OAAC,wJAAM;;;;;;;;;;;;;;;oCAIZ,gCACC,8OAAC;wCAAO,SAAS,IAAM,eAAe,KAAK,EAAE;kDAC3C,cAAA,8OAAC;sDACC,cAAA,8OAAC,0JAAQ;;;;;;;;;;;;;;;;;;;;;4BAKhB,KAAK,eAAe,CAAC,SAAS,KAAK,mBAAK,8OAAC;0CAAE;;;;;;;;;;;;;eAlEvC,KAAK,EAAE;;;;;;;;;;AAwExB;uCAEe", "debugId": null}}, {"offset": {"line": 518, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cart__checkout\": \"PriceSummary-module-scss-module__7p8iVa__cart__checkout\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/utils/tooltip/Tooltip.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"tooltip\": \"Tooltip-module-scss-module__vdbe-W__tooltip\",\n  \"tooltip--bottom\": \"Tooltip-module-scss-module__vdbe-W__tooltip--bottom\",\n  \"tooltip--condition\": \"Tooltip-module-scss-module__vdbe-W__tooltip--condition\",\n  \"tooltip--hover\": \"Tooltip-module-scss-module__vdbe-W__tooltip--hover\",\n  \"tooltip--left\": \"Tooltip-module-scss-module__vdbe-W__tooltip--left\",\n  \"tooltip--right\": \"Tooltip-module-scss-module__vdbe-W__tooltip--right\",\n  \"tooltip--top\": \"Tooltip-module-scss-module__vdbe-W__tooltip--top\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 537, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/tooltip/Tooltip.tsx"], "sourcesContent": ["import React, { ReactNode } from 'react'\nimport styles from './Tooltip.module.scss'\n\ninterface TooltipProps {\n  children: ReactNode\n  content: string\n  position?: 'top' | 'bottom' | 'left' | 'right'\n  disabled?: boolean\n  className?: string\n  showOnHover?: boolean\n  showOnCondition?: boolean\n}\n\nconst Tooltip: React.FC<TooltipProps> = ({\n  children,\n  content,\n  position = 'top',\n  disabled = false,\n  className = '',\n  showOnHover = true,\n  showOnCondition = false\n}) => {\n  // Don't show tooltip if disabled or no content\n  if (disabled || !content) {\n    return <>{children}</>\n  }\n\n  const tooltipClasses = [\n    styles.tooltip,\n    styles[`tooltip--${position}`],\n    showOnHover ? styles['tooltip--hover'] : '',\n    showOnCondition ? styles['tooltip--condition'] : '',\n    className\n  ].filter(Boolean).join(' ')\n\n  return (\n    <span className={tooltipClasses} data-tooltip={content}>\n      {children}\n    </span>\n  )\n}\n\nexport default Tooltip\n"], "names": [], "mappings": ";;;;;AACA;;;AAYA,MAAM,UAAkC,CAAC,EACvC,QAAQ,EACR,OAAO,EACP,WAAW,KAAK,EAChB,WAAW,KAAK,EAChB,YAAY,EAAE,EACd,cAAc,IAAI,EAClB,kBAAkB,KAAK,EACxB;IACC,+CAA+C;IAC/C,IAAI,YAAY,CAAC,SAAS;QACxB,qBAAO;sBAAG;;IACZ;IAEA,MAAM,iBAAiB;QACrB,wKAAM,CAAC,OAAO;QACd,wKAAM,CAAC,CAAC,SAAS,EAAE,UAAU,CAAC;QAC9B,cAAc,wKAAM,CAAC,iBAAiB,GAAG;QACzC,kBAAkB,wKAAM,CAAC,qBAAqB,GAAG;QACjD;KACD,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;IAEvB,qBACE,8OAAC;QAAK,WAAW;QAAgB,gBAAc;kBAC5C;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 574, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/%28shop%29/%28checkout-process%29/components/price-summary/PriceSummary.tsx"], "sourcesContent": ["import React from 'react'\r\nimport { RiQuestionFill } from 'react-icons/ri'\r\nimport styles from './PriceSummary.module.scss'\r\nimport Tooltip from '@/src/components/utils/tooltip/Tooltip'\r\n\r\ninterface PriceSummaryProps {\r\n  totalPrice: number\r\n  shippingCost: number \r\n  // grandTotal: number\r\n  item_count: number\r\n  cart_weight: number\r\n  onCheckout?: () => void\r\n}\r\n\r\nconst PriceSummary: React.FC<PriceSummaryProps> = ({\r\n  totalPrice,\r\n  item_count,\r\n  cart_weight,\r\n  onCheckout,\r\n}) => {\r\n  return (\r\n    <div className={styles.cart__checkout}>\r\n      <div>\r\n        <p>Item count: </p> <p>{item_count}</p>\r\n      </div>\r\n\r\n      <div>\r\n        <p>\r\n          Cart weight:\r\n          <Tooltip\r\n            content={`Shipping cost will be calculated after finalizing adding items to the cart.`}\r\n            position='top'\r\n          >\r\n            <i>\r\n              <RiQuestionFill />\r\n            </i>\r\n          </Tooltip>\r\n        </p>\r\n        <p>{cart_weight}g</p>\r\n      </div>\r\n\r\n      <div>\r\n        <p>Item&apos;s cost: </p> <p>${totalPrice.toFixed(2)}</p>\r\n      </div>\r\n      {onCheckout && <button onClick={onCheckout}>Proceed to checkout</button>}\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default PriceSummary\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;;;AAWA,MAAM,eAA4C,CAAC,EACjD,UAAU,EACV,UAAU,EACV,WAAW,EACX,UAAU,EACX;IACC,qBACE,8OAAC;QAAI,WAAW,oNAAM,CAAC,cAAc;;0BACnC,8OAAC;;kCACC,8OAAC;kCAAE;;;;;;oBAAgB;kCAAC,8OAAC;kCAAG;;;;;;;;;;;;0BAG1B,8OAAC;;kCACC,8OAAC;;4BAAE;0CAED,8OAAC,4JAAO;gCACN,SAAS,CAAC,2EAA2E,CAAC;gCACtF,UAAS;0CAET,cAAA,8OAAC;8CACC,cAAA,8OAAC,gKAAc;;;;;;;;;;;;;;;;;;;;;kCAIrB,8OAAC;;4BAAG;4BAAY;;;;;;;;;;;;;0BAGlB,8OAAC;;kCACC,8OAAC;kCAAE;;;;;;oBAAsB;kCAAC,8OAAC;;4BAAE;4BAAE,WAAW,OAAO,CAAC;;;;;;;;;;;;;YAEnD,4BAAc,8OAAC;gBAAO,SAAS;0BAAY;;;;;;;;;;;;AAGlD;uCAEe", "debugId": null}}, {"offset": {"line": 705, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/%28shop%29/%28checkout-process%29/checkout/address-choice/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport styles from './AddressStage.module.scss'\nimport authStore from '@/src/stores/auth-store'\nimport { useCustomerDetails } from '@/src/hooks/customer-hooks'\nimport cartStore from '@/src/stores/cart-store'\nimport { useCart, useDeleteCartItem } from '@/src/hooks/cart-hooks'\nimport { AddressFormInputs } from '@/src/components/account/addresses/ManageAddresses'\nimport EmptyCart from '@/src/components/utils/empty-cart/EmptyCart'\nimport Alert from '@/src/components/utils/alert/Alert'\nimport Logo from '@/src/components/utils/logo/Logo'\nimport Spinner from '@/src/components/utils/spinner/Spinner'\nimport Link from 'next/link'\nimport CartItemsList from '../../components/cart/CartItemsList'\nimport PriceSummary from '../../components/price-summary/PriceSummary'\n\nconst AddressChoice = () => {\n  const router = useRouter()\n\n  const { isLoggedIn } = authStore()\n  const { data: customer } = useCustomerDetails(isLoggedIn)\n  const { cartId, setSelectedAddress, selectedAddress } = cartStore()\n  const { isPending, error, data } = useCart()\n  const { mutate: deleteCartItem } = useDeleteCartItem()\n\n  const [addressesReady, setAddressesReady] = useState(false)\n\n  useEffect(() => {\n    if (!isLoggedIn) {\n      router.push('/login')\n    }\n  }, [isLoggedIn, router])\n\n  useEffect(() => {\n    if (customer?.address && customer.address.length > 0) {\n      if (!selectedAddress || Object.keys(selectedAddress).length === 0) {\n        setSelectedAddress(customer.address[0])\n      }\n      setAddressesReady(true)\n    }\n  }, [customer, selectedAddress, setSelectedAddress])\n\n  // Handle out-of-stock items by removing them\n  useEffect(() => {\n    if (data && data?.cart_items?.length > 0) {\n      const outOfStockItems = data.cart_items.filter(\n        (item) => item.product_variant.stock_qty === 0\n      )\n\n      if (outOfStockItems.length > 0) {\n        outOfStockItems.forEach((item) => {\n          deleteCartItem(item.id) // Remove each out-of-stock item from the cart\n        })\n      }\n    }\n  }, [data, deleteCartItem])\n\n  const handleAddressChange = (address: AddressFormInputs) => {\n    setSelectedAddress(address)\n  }\n\n  return (\n    <>\n      {!cartId ? (\n        <EmptyCart message='Your cart is empty. Add some products to the cart to checkout!' />\n      ) : (\n        <>\n          {isPending ? (\n            <Spinner color='#0091CF' size={20} loading />\n          ) : (\n            <>\n              {error ? (\n                <Alert variant='error' message={error.message} />\n              ) : (\n                <>\n                  {customer?.address?.length === 0 ||\n                  customer?.phone_number === '' ? (\n                    <>\n                      <div className='logo_header'>\n                        <Logo />\n                      </div>\n                      <div className={styles.missing_addresses}>\n                        <Alert\n                          variant='warning'\n                          textSize='20'\n                          message=\"\n                        You haven't added a shipping address yet. \n                        Please add one along with a phone number to continue with checkout. Thank you! 😊\"\n                        />\n                        <section className='btn_container'>\n                          <button\n                            className='empty_btn'\n                            onClick={() => router.push('/account/profile')}\n                          >\n                            Update Profile\n                          </button>\n                        </section>\n                      </div>\n                    </>\n                  ) : (\n                    <>\n                      {!data || data.cart_items.length === 0 ? (\n                        <div className={styles.empty_cart}>\n                          <p>\n                            Your cart is empty. Add some products to the cart to\n                            checkout!\n                          </p>\n                          <Link href='/'>Go Shopping </Link>\n                        </div>\n                      ) : (\n                        <>\n                          <section>\n                            <section\n                              className={`container ${styles.address_stage}`}\n                            >\n                              <h3>Address Choices</h3>\n                              <div className={styles.contact_details}>\n                                <h3>Contact Details: </h3>\n                                <p>\n                                  Deliver to: {customer?.first_name}{' '}\n                                  {customer?.last_name}\n                                </p>\n                                <p>Phone: {customer?.phone_number}</p>\n                                <p>Email to: {customer?.email}</p>\n                              </div>\n                              <hr />\n                              <div className={styles.cart}>\n                                <CartItemsList cartItems={data.cart_items} />\n                                <PriceSummary\n                                  totalPrice={data?.total_price}\n                                  // shippingCost={data?.shipping_cost}\n                                  // grandTotal={data?.grand_total}\n                                  item_count={data?.item_count}\n                                  cart_weight={data?.cart_weight}\n                                />\n                              </div>\n                              <hr />\n                              {/* Render the addresses only when addresses are ready */}\n                              {addressesReady && (\n                                <div className={styles.address_selection}>\n                                  <h3>Choose a shipping address: </h3>\n                                  {customer?.address?.map((address) => (\n                                    <address key={address.id}>\n                                      <input\n                                        type='radio'\n                                        id={`address-${address.id}`}\n                                        name='address'\n                                        value={address.id}\n                                        checked={\n                                          selectedAddress?.id === address.id\n                                        }\n                                        onChange={() =>\n                                          handleAddressChange(address)\n                                        }\n                                      />\n                                      <label htmlFor={`address-${address.id}`}>\n                                        {address.full_name},{' '}\n                                        {address.street_name},{' '}\n                                        {address.city_or_village}\n                                      </label>\n                                    </address>\n                                  ))}\n                                  <button\n                                    onClick={() =>\n                                      router.push('/checkout/payment-choice')\n                                    }\n                                  >\n                                    Use this address\n                                  </button>\n                                </div>\n                              )}\n                              <hr />\n                            </section>\n                          </section>\n                        </>\n                      )}\n                    </>\n                  )}\n                </>\n              )}\n            </>\n          )}\n        </>\n      )}\n    </>\n  )\n}\n\nexport default AddressChoice\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA;;;;;;;;;;;;;;;;AAkBA,MAAM,gBAAgB;IACpB,MAAM,SAAS,IAAA,+IAAS;IAExB,MAAM,EAAE,UAAU,EAAE,GAAG,IAAA,yIAAS;IAChC,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,IAAA,uJAAkB,EAAC;IAC9C,MAAM,EAAE,MAAM,EAAE,kBAAkB,EAAE,eAAe,EAAE,GAAG,IAAA,yIAAS;IACjE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,IAAA,wIAAO;IAC1C,MAAM,EAAE,QAAQ,cAAc,EAAE,GAAG,IAAA,kJAAiB;IAEpD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,iNAAQ,EAAC;IAErD,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,YAAY;YACf,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAY;KAAO;IAEvB,IAAA,kNAAS,EAAC;QACR,IAAI,UAAU,WAAW,SAAS,OAAO,CAAC,MAAM,GAAG,GAAG;YACpD,IAAI,CAAC,mBAAmB,OAAO,IAAI,CAAC,iBAAiB,MAAM,KAAK,GAAG;gBACjE,mBAAmB,SAAS,OAAO,CAAC,EAAE;YACxC;YACA,kBAAkB;QACpB;IACF,GAAG;QAAC;QAAU;QAAiB;KAAmB;IAElD,6CAA6C;IAC7C,IAAA,kNAAS,EAAC;QACR,IAAI,QAAQ,MAAM,YAAY,SAAS,GAAG;YACxC,MAAM,kBAAkB,KAAK,UAAU,CAAC,MAAM,CAC5C,CAAC,OAAS,KAAK,eAAe,CAAC,SAAS,KAAK;YAG/C,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,gBAAgB,OAAO,CAAC,CAAC;oBACvB,eAAe,KAAK,EAAE,GAAE,8CAA8C;gBACxE;YACF;QACF;IACF,GAAG;QAAC;QAAM;KAAe;IAEzB,MAAM,sBAAsB,CAAC;QAC3B,mBAAmB;IACrB;IAEA,qBACE;kBACG,CAAC,uBACA,8OAAC,oKAAS;YAAC,SAAQ;;;;;qEAEnB;sBACG,0BACC,8OAAC,4JAAO;gBAAC,OAAM;gBAAU,MAAM;gBAAI,OAAO;;;;;yEAE1C;0BACG,sBACC,8OAAC,wJAAK;oBAAC,SAAQ;oBAAQ,SAAS,MAAM,OAAO;;;;;6EAE7C;8BACG,UAAU,SAAS,WAAW,KAC/B,UAAU,iBAAiB,mBACzB;;0CACE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,sJAAI;;;;;;;;;;0CAEP,8OAAC;gCAAI,WAAW,mNAAM,CAAC,iBAAiB;;kDACtC,8OAAC,wJAAK;wCACJ,SAAQ;wCACR,UAAS;wCACT,SAAQ;;;;;;kDAIV,8OAAC;wCAAQ,WAAU;kDACjB,cAAA,8OAAC;4CACC,WAAU;4CACV,SAAS,IAAM,OAAO,IAAI,CAAC;sDAC5B;;;;;;;;;;;;;;;;;;qDAOP;kCACG,CAAC,QAAQ,KAAK,UAAU,CAAC,MAAM,KAAK,kBACnC,8OAAC;4BAAI,WAAW,mNAAM,CAAC,UAAU;;8CAC/B,8OAAC;8CAAE;;;;;;8CAIH,8OAAC,uKAAI;oCAAC,MAAK;8CAAI;;;;;;;;;;;qFAGjB;sCACE,cAAA,8OAAC;0CACC,cAAA,8OAAC;oCACC,WAAW,CAAC,UAAU,EAAE,mNAAM,CAAC,aAAa,EAAE;;sDAE9C,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;4CAAI,WAAW,mNAAM,CAAC,eAAe;;8DACpC,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;;wDAAE;wDACY,UAAU;wDAAY;wDAClC,UAAU;;;;;;;8DAEb,8OAAC;;wDAAE;wDAAQ,UAAU;;;;;;;8DACrB,8OAAC;;wDAAE;wDAAW,UAAU;;;;;;;;;;;;;sDAE1B,8OAAC;;;;;sDACD,8OAAC;4CAAI,WAAW,mNAAM,CAAC,IAAI;;8DACzB,8OAAC,6LAAa;oDAAC,WAAW,KAAK,UAAU;;;;;;8DACzC,8OAAC,wMAAY;oDACX,YAAY,MAAM;oDAClB,qCAAqC;oDACrC,iCAAiC;oDACjC,YAAY,MAAM;oDAClB,aAAa,MAAM;;;;;;;;;;;;sDAGvB,8OAAC;;;;;wCAEA,gCACC,8OAAC;4CAAI,WAAW,mNAAM,CAAC,iBAAiB;;8DACtC,8OAAC;8DAAG;;;;;;gDACH,UAAU,SAAS,IAAI,CAAC,wBACvB,8OAAC;;0EACC,8OAAC;gEACC,MAAK;gEACL,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;gEAC3B,MAAK;gEACL,OAAO,QAAQ,EAAE;gEACjB,SACE,iBAAiB,OAAO,QAAQ,EAAE;gEAEpC,UAAU,IACR,oBAAoB;;;;;;0EAGxB,8OAAC;gEAAM,SAAS,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;;oEACpC,QAAQ,SAAS;oEAAC;oEAAE;oEACpB,QAAQ,WAAW;oEAAC;oEAAE;oEACtB,QAAQ,eAAe;;;;;;;;uDAhBd,QAAQ,EAAE;;;;;8DAoB1B,8OAAC;oDACC,SAAS,IACP,OAAO,IAAI,CAAC;8DAEf;;;;;;;;;;;;sDAKL,8OAAC;;;;;;;;;;;;;;;;;;;;;;AAe/B;uCAEe", "debugId": null}}]}