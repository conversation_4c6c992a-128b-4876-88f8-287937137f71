(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/src/providers/react-query-provider.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>ReactQueryProvider
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2d$devtools$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query-devtools/build/modern/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function ReactQueryProvider(param) {
    let { children } = param;
    _s();
    const [queryClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "ReactQueryProvider.useState": ()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClient"]()
    }["ReactQueryProvider.useState"]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClientProvider"], {
        client: queryClient,
        children: [
            children,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2d$devtools$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReactQueryDevtools"], {}, void 0, false, {
                fileName: "[project]/src/providers/react-query-provider.tsx",
                lineNumber: 17,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/providers/react-query-provider.tsx",
        lineNumber: 15,
        columnNumber: 5
    }, this);
}
_s(ReactQueryProvider, "qFhNRSk+4hqflxYLL9+zYF5AcuQ=");
_c = ReactQueryProvider;
var _c;
__turbopack_context__.k.register(_c, "ReactQueryProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/stores/auth-store.ts [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
;
;
const authStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["devtools"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])((set)=>({
        isLoggedIn: false,
        username: null,
        regInitiated: false,
        verificationCodeSubmitted: false,
        passwordSubmitted: false,
        customerDetailsSubmitted: false,
        updateAuthInfoSubmitted: false,
        // altUsername: null,
        authInfoVerifyCodeSubmitted: false,
        setIsLoggedIn: (isLoggedIn)=>{
            set({
                isLoggedIn
            });
        },
        // logout: () => {
        //   set({ isLoggedIn: false })
        // },
        setUsername: (username)=>{
            set({
                username
            });
        },
        setRegInitiated: (regInitiated)=>{
            set({
                regInitiated
            });
        },
        setVerificationCodeSubmitted: (verificationCodeSubmitted)=>{
            set({
                verificationCodeSubmitted
            });
        },
        setPasswordSubmitted: (passwordSubmitted)=>{
            set({
                passwordSubmitted
            });
        },
        setCustomerDetailsSubmitted: (customerDetailsSubmitted)=>{
            set({
                customerDetailsSubmitted
            });
        },
        setUpdateAuthInfoSubmitted: (updateAuthInfoSubmitted)=>{
            set({
                updateAuthInfoSubmitted
            });
        },
        // setAltUsername: (altUsername: string | null) => {
        //   set({ altUsername })
        // },
        setAuthInfoVerifyCodeSubmitted: (authInfoVerifyCodeSubmitted)=>{
            set({
                authInfoVerifyCodeSubmitted
            });
        }
    }), {
    name: 'auth_store',
    partialize: (state)=>({
            isLoggedIn: state.isLoggedIn
        })
})));
const __TURBOPACK__default__export__ = authStore;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/lib/api-client.ts [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
;
;
const axiosInstance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: ("TURBOPACK compile-time value", "http://localhost:8000/api"),
    withCredentials: true
});
class APIClient {
    constructor(endpoint){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "endpoint", void 0);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "get", async (config)=>{
            try {
                const response = await axiosInstance.get(this.endpoint, config);
                return response.data;
            } catch (error) {
                this.handleError(error);
                // Add a return statement to ensure a return value in case of an error
                throw new Error(error.message);
            }
        });
        // This is for paginated responses from the server
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "getAll", async (config)=>{
            try {
                const response = await axiosInstance.get(this.endpoint, config);
                console.log("".concat(response.config.url));
                return response.data;
            } catch (error) {
                this.handleError(error);
                // Add a return statement to ensure a return value in case of an error
                throw new Error(error.message);
            }
        });
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "post", async (data, config)=>{
            try {
                const response = await axiosInstance.post(this.endpoint, data, config);
                return response.data;
            } catch (error) {
                this.handleError(error);
                // Add a return statement to ensure a return value in case of an error
                throw new Error(error.message);
            }
        });
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "patch", async (data, config)=>{
            try {
                const response = await axiosInstance.patch(this.endpoint, data, config);
                return response.data;
            } catch (error) {
                this.handleError(error);
                throw new Error(error.message);
            }
        });
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "delete", async (itemId)=>{
            try {
                const response = await axiosInstance.delete("".concat(this.endpoint, "/").concat(itemId, "/"));
                return response.data;
            } catch (error) {
                this.handleError(error);
                // Add a return statement to ensure a return value in case of an error
                throw new Error(error.message);
            }
        });
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "handleError", (error)=>{
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isAxiosError(error)) {
                var _error_response, _error_response1;
                // ✅ Don't log cancelled requests as errors
                if (error.code === 'ERR_CANCELED' || error.message === 'canceled') {
                    // This is a cancelled request, don't log it as an error
                    throw error;
                }
                // Don't log expected authentication errors to reduce console noise
                const isAuthError = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401 || ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 403;
                if (!isAuthError) {
                    console.error("Error message: ", error.message);
                    if (error.response) {
                        console.error("Response data: ", error.response.data);
                        console.error("Response status: ", error.response.status);
                    } else if (error.request) {
                        console.error("Request data: ", error.request);
                    } else {
                        console.error("Error config: ", error.config);
                    }
                }
                throw {
                    message: error.message,
                    response: error.response
                };
            } else {
                console.error("Error: ", error);
            }
            throw error;
        });
        this.endpoint = endpoint;
    }
}
const __TURBOPACK__default__export__ = APIClient;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/constants/constants.ts [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "CACHE_KEY_CART_ITEMS",
    ()=>CACHE_KEY_CART_ITEMS,
    "CACHE_KEY_ORDERS",
    ()=>CACHE_KEY_ORDERS,
    "CACHE_KEY_ORDERS_ADMIN",
    ()=>CACHE_KEY_ORDERS_ADMIN,
    "CACHE_KEY_ORDER_ITEMS",
    ()=>CACHE_KEY_ORDER_ITEMS,
    "CACHE_KEY_PRODUCTS",
    ()=>CACHE_KEY_PRODUCTS,
    "CUSTOMER_ADDRESSES",
    ()=>CUSTOMER_ADDRESSES,
    "CUSTOMER_DETAILS",
    ()=>CUSTOMER_DETAILS,
    "ITEMS_PER_PAGE",
    ()=>ITEMS_PER_PAGE,
    "WISHLIST_ITEMS",
    ()=>WISHLIST_ITEMS
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const CACHE_KEY_CART_ITEMS = 'cart_items';
const CACHE_KEY_ORDER_ITEMS = 'order_items';
const CUSTOMER_DETAILS = 'customer_details';
const CACHE_KEY_ORDERS = 'orders';
const CUSTOMER_ADDRESSES = 'customer_addresses';
const CACHE_KEY_ORDERS_ADMIN = 'orders_admin';
const CACHE_KEY_PRODUCTS = 'products';
const ITEMS_PER_PAGE = Number(("TURBOPACK compile-time value", "2"));
const WISHLIST_ITEMS = 'wishlist_items';
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/hooks/auth-hooks.ts [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "useChangeAuthInfo",
    ()=>useChangeAuthInfo,
    "useChangePassword",
    ()=>useChangePassword,
    "useInitResetPassword",
    ()=>useInitResetPassword,
    "useLogin",
    ()=>useLogin,
    "useLogout",
    ()=>useLogout,
    "useRegister",
    ()=>useRegister,
    "useResetPasswordConfirm",
    ()=>useResetPasswordConfirm,
    "useSendAuthInfoVerifyCode",
    ()=>useSendAuthInfoVerifyCode,
    "useSendUpdateAuthInfo",
    ()=>useSendUpdateAuthInfo,
    "useSendVerifyCode",
    ()=>useSendVerifyCode,
    "useSendVerifyRegCredentials",
    ()=>useSendVerifyRegCredentials,
    "useSetPassword",
    ()=>useSetPassword
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/auth-store.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/constants.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature(), _s6 = __turbopack_context__.k.signature(), _s7 = __turbopack_context__.k.signature(), _s8 = __turbopack_context__.k.signature(), _s9 = __turbopack_context__.k.signature(), _s10 = __turbopack_context__.k.signature(), _s11 = __turbopack_context__.k.signature();
;
;
;
;
const useRegister = ()=>{
    _s();
    const { setUsername } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    // In AuthClient Response has defined as: Request = Response 
    // If request data is different do not forget to specify the types here. 
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("/auth/register/initiate/");
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useRegister.useMutation[mutation]": (data)=>apiClient.post(data)
        }["useRegister.useMutation[mutation]"],
        onSuccess: {
            "useRegister.useMutation[mutation]": (data)=>{
                console.log(data);
                setUsername(data.username);
            }
        }["useRegister.useMutation[mutation]"]
    });
    return {
        mutation
    };
};
_s(useRegister, "Kvw+Q3+Z705KOo+cvu2gXTQDxwg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useSendVerifyRegCredentials = ()=>{
    _s1();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('/auth/register/verify/');
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useSendVerifyRegCredentials.useMutation[mutation]": (data)=>apiClient.post(data)
        }["useSendVerifyRegCredentials.useMutation[mutation]"]
    });
    return {
        mutation
    };
};
_s1(useSendVerifyRegCredentials, "Kvw+Q3+Z705KOo+cvu2gXTQDxwg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useSetPassword = ()=>{
    _s2();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("/auth/register/set-password/");
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useSetPassword.useMutation[mutation]": (data)=>apiClient.post(data)
        }["useSetPassword.useMutation[mutation]"]
    });
    return {
        mutation
    };
};
_s2(useSetPassword, "Kvw+Q3+Z705KOo+cvu2gXTQDxwg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useSendUpdateAuthInfo = ()=>{
    _s3();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('/auth/profile/contact/update/');
    // const { setAltUsername } = authStore()
    const authInfoMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useSendUpdateAuthInfo.useMutation[authInfoMutation]": (data)=>apiClient.patch(data)
        }["useSendUpdateAuthInfo.useMutation[authInfoMutation]"]
    });
    return {
        authInfoMutation
    };
};
_s3(useSendUpdateAuthInfo, "T/eQX0z8XYYNHRdu5U2rZYdnA08=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useSendVerifyCode = ()=>{
    _s4();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('/auth/profile/contact/verify/');
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useSendVerifyCode.useMutation[mutation]": (data)=>apiClient.post(data)
        }["useSendVerifyCode.useMutation[mutation]"]
    });
    return {
        mutation
    };
};
_s4(useSendVerifyCode, "Kvw+Q3+Z705KOo+cvu2gXTQDxwg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useLogin = ()=>{
    _s5();
    const { setIsLoggedIn } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("/auth/login/");
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useLogin.useMutation[mutation]": (data)=>apiClient.post(data)
        }["useLogin.useMutation[mutation]"],
        onSuccess: {
            "useLogin.useMutation[mutation]": ()=>{
                // const { access } = data.data
                setIsLoggedIn(true);
            }
        }["useLogin.useMutation[mutation]"]
    });
    return {
        mutation
    };
};
_s5(useLogin, "Kvw+Q3+Z705KOo+cvu2gXTQDxwg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useLogout = ()=>{
    _s6();
    const { setIsLoggedIn } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("/auth/logout/");
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useLogout.useMutation[mutation]": ()=>apiClient.post()
        }["useLogout.useMutation[mutation]"],
        onSuccess: {
            "useLogout.useMutation[mutation]": ()=>{
                console.log('Logout was success');
                setIsLoggedIn(false);
                queryClient.invalidateQueries({
                    queryKey: [
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CUSTOMER_DETAILS"]
                    ]
                });
                // queryClient.refetchQueries({
                //   queryKey: [CUSTOMER_DETAILS]
                // })
                queryClient.removeQueries({
                    queryKey: [
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CUSTOMER_DETAILS"]
                    ]
                });
            }
        }["useLogout.useMutation[mutation]"]
    });
    return {
        mutation
    };
};
_s6(useLogout, "mUUakYvFQSudGRmMzZErOI9nuuc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useChangePassword = ()=>{
    _s7();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("/auth/password/change/");
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useChangePassword.useMutation[mutation]": (data)=>apiClient.post(data)
        }["useChangePassword.useMutation[mutation]"]
    });
    return {
        mutation
    };
};
_s7(useChangePassword, "Kvw+Q3+Z705KOo+cvu2gXTQDxwg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useInitResetPassword = ()=>{
    _s8();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("/auth/password/reset/request/");
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useInitResetPassword.useMutation[mutation]": (newPassword)=>apiClient.post(newPassword)
        }["useInitResetPassword.useMutation[mutation]"]
    });
    return {
        mutation
    };
};
_s8(useInitResetPassword, "Kvw+Q3+Z705KOo+cvu2gXTQDxwg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useResetPasswordConfirm = ()=>{
    _s9();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("/auth/password/reset/confirm/");
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useResetPasswordConfirm.useMutation[mutation]": (data)=>apiClient.post(data)
        }["useResetPasswordConfirm.useMutation[mutation]"]
    });
    return {
        mutation
    };
};
_s9(useResetPasswordConfirm, "Kvw+Q3+Z705KOo+cvu2gXTQDxwg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useChangeAuthInfo = ()=>{
    _s10();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("auth/profile/contact/update/");
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useChangeAuthInfo.useMutation[mutation]": (data)=>apiClient.patch(data)
        }["useChangeAuthInfo.useMutation[mutation]"],
        onSuccess: {
            "useChangeAuthInfo.useMutation[mutation]": ()=>{
            // const { access } = data.data
            // login()
            }
        }["useChangeAuthInfo.useMutation[mutation]"]
    });
    return {
        mutation
    };
};
_s10(useChangeAuthInfo, "Kvw+Q3+Z705KOo+cvu2gXTQDxwg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useSendAuthInfoVerifyCode = ()=>{
    _s11();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('/auth/profile/contact/verify/');
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useSendAuthInfoVerifyCode.useMutation[mutation]": (data)=>apiClient.post(data)
        }["useSendAuthInfoVerifyCode.useMutation[mutation]"]
    });
    return {
        mutation
    };
};
_s11(useSendAuthInfoVerifyCode, "Kvw+Q3+Z705KOo+cvu2gXTQDxwg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/stores/cart-store.ts [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
;
;
// {
//   title: title,
//   product_image: `https://res.cloudinary.com/dev-kani/${item.product_image[0]?.image}`,
//   quantity: 1,
//   price: item.price,
//   sku: item.sku
// }
const cartStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])((set)=>({
        cartId: null,
        selectedVariant: null,
        cartItem: {
            id: null,
            product_id: null,
            product_variant: null,
            quantity: null,
            extra_data: {}
        },
        customer: null,
        selectedAddress: null,
        // paymentOptions: [], // unused
        selectedPaymentOption: null,
        setCustomer: (customer)=>set({
                customer: customer
            }),
        setSelectedAddress: (address)=>set({
                selectedAddress: address
            }),
        // setPaymentOptions: (option) => set({ paymentOptions: option }),
        setSelectedPaymentOption: (paymentOption)=>set({
                selectedPaymentOption: paymentOption
            }),
        setCartId: (newCartId)=>set({
                cartId: newCartId
            }),
        // setCartItems: (cartItemData) => set({ selectedVariant: cartItemData }),
        setCartItemId: (id)=>set((state)=>({
                    cartItem: {
                        ...state.cartItem,
                        id
                    }
                })),
        // setProductId: (product_id) => set((state) => ({
        //   cartItems: { ...state.cartItems, product_id }
        // })),
        // setProductVariant: (product_variant) => set((state) => ({
        //   cartItems: { ...state.cartItems, product_variant },
        // })),
        setProductVariant: (product_variant)=>set({
                selectedVariant: product_variant
            }),
        // setQuantity: (quantity) => set((state) => ({
        //   cartItems: { ...state.cartItems, quantity }
        // })),
        setExtraData: (extra_data)=>set((state)=>({
                    cartItem: {
                        ...state.cartItem,
                        extra_data
                    }
                })),
        resetExtraData: ()=>set((state)=>({
                    cartItem: {
                        ...state.cartItem,
                        extra_data: {}
                    }
                }))
    }), {
    name: 'cart_store'
}));
const __TURBOPACK__default__export__ = cartStore;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/hooks/cart-hooks.ts [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "useAddToCart",
    ()=>useAddToCart,
    "useCart",
    ()=>useCart,
    "useDeleteCartItem",
    ()=>useDeleteCartItem,
    "useUpdateCart",
    ()=>useUpdateCart
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/cart-store.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/constants.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature();
;
;
;
;
const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('/cart/');
const useAddToCart = (product, qty)=>{
    _s();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const { cartId, setCartId, setCartItemId, selectedVariant, cartItem } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const { extra_data } = cartItem;
    const apiClient2 = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("/cart/".concat(cartId, "/items/"));
    console.log(cartItem);
    console.log(extra_data);
    const addToCartMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useAddToCart.useMutation[addToCartMutation]": (param)=>{
                let { product_id, product_variant, quantity, extra_data } = param;
                return apiClient2.post({
                    // cart_id,
                    product_id,
                    product_variant: product_variant,
                    quantity,
                    extra_data
                });
            }
        }["useAddToCart.useMutation[addToCartMutation]"],
        onSuccess: {
            "useAddToCart.useMutation[addToCartMutation]": (data)=>{
                setCartItemId(parseInt(data.id));
                queryClient.invalidateQueries({
                    queryKey: [
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_KEY_CART_ITEMS"]
                    ]
                });
            }
        }["useAddToCart.useMutation[addToCartMutation]"],
        onError: {
            "useAddToCart.useMutation[addToCartMutation]": (error)=>{
                console.error('Error adding item to cart:', error);
            }
        }["useAddToCart.useMutation[addToCartMutation]"]
    });
    const createCartMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useAddToCart.useMutation[createCartMutation]": ()=>apiClient.post({})
        }["useAddToCart.useMutation[createCartMutation]"],
        onSuccess: {
            "useAddToCart.useMutation[createCartMutation]": (data)=>{
                if (!selectedVariant) {
                    return;
                }
                console.log('cart id (res) in createCartMutation', data);
                setCartId(data.id);
                addToCartMutation.mutate({
                    // cart_id: data.id,
                    product_id: product.id,
                    product_variant: selectedVariant.id,
                    quantity: qty,
                    extra_data: extra_data
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_KEY_CART_ITEMS"]
                    ]
                });
            }
        }["useAddToCart.useMutation[createCartMutation]"],
        onError: {
            "useAddToCart.useMutation[createCartMutation]": (error)=>{
                console.error('Error creating cart:', error);
            }
        }["useAddToCart.useMutation[createCartMutation]"]
    });
    const handleAddToCart = ()=>{
        if (!selectedVariant) {
            return; // Early return if no variant selected
        }
        if (cartId) {
            addToCartMutation.mutate({
                // cart_id: cartId,
                product_id: product.id,
                product_variant: selectedVariant.id,
                quantity: qty,
                extra_data: extra_data
            });
        } else {
            createCartMutation.mutate({});
        }
    };
    return {
        handleAddToCart,
        isPending: addToCartMutation.isPending || createCartMutation.isPending,
        error: addToCartMutation.error || createCartMutation.error
    };
};
_s(useAddToCart, "MY4XVZbalqTKsv6UC3DMGssROj4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useCart = ()=>{
    _s1();
    const { cartId } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("/cart/".concat(cartId));
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_KEY_CART_ITEMS"],
            cartId
        ],
        queryFn: apiClient.get,
        enabled: !!cartId,
        // keepPreviousData: true,
        // staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours
        // initialData:  Here we can add categories as static data
        // refetchOnMount: true,
        staleTime: 0
    });
};
_s1(useCart, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useDeleteCartItem = ()=>{
    _s2();
    const { cartId } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("/cart/".concat(cartId, "/items"));
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useDeleteCartItem.useMutation": (itemId)=>apiClient.delete(itemId)
        }["useDeleteCartItem.useMutation"],
        // onMutate: async (itemId) => {
        //   await queryClient.cancelQueries(CACHE_KEY_CART_ITEMS)
        //   const previousCartItems = queryClient.getQueryData(CACHE_KEY_CART_ITEMS)
        //   queryClient.setQueryData(CACHE_KEY_CART_ITEMS, (oldData: CartShape) => {
        //     const updatedCartItems = oldData?.cart_items.filter(
        //       (item) => item.id !== itemId
        //     )
        //     return { ...oldData, cart_items: updatedCartItems }
        //   })
        //   return { previousCartItems }
        // },
        onSuccess: {
            "useDeleteCartItem.useMutation": ()=>{
                // queryClient.invalidateQueries(CACHE_KEY_CART_ITEMS)
                queryClient.invalidateQueries({
                    queryKey: [
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_KEY_CART_ITEMS"]
                    ]
                });
            }
        }["useDeleteCartItem.useMutation"]
    });
// const handleDeleteCartItem = (itemId) => {
//   mutate({ itemId })
// }
// return { isPending, isError, mutate }
};
_s2(useDeleteCartItem, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useUpdateCart = ()=>{
    _s3();
    const { cartId } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const handleQuantityUpdate = (itemId, newQuantity)=>{
        const cartItemApiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("/cart/".concat(cartId, "/items/").concat(itemId, "/")) // Item-specific endpoint
        ;
        mutation.mutate({
            itemId,
            newQuantity,
            apiClient: cartItemApiClient
        });
    };
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useUpdateCart.useMutation[mutation]": (param)=>{
                let { newQuantity, apiClient } = param;
                // Using the specific APIClient instance for this item
                return apiClient.patch({
                    quantity: newQuantity
                });
            }
        }["useUpdateCart.useMutation[mutation]"],
        onSuccess: {
            "useUpdateCart.useMutation[mutation]": ()=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_KEY_CART_ITEMS"]
                    ]
                });
            }
        }["useUpdateCart.useMutation[mutation]"]
    });
    return {
        mutation,
        handleQuantityUpdate
    };
};
_s3(useUpdateCart, "mUUakYvFQSudGRmMzZErOI9nuuc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/hooks/customer-hooks.ts [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "useCustomerDetails",
    ()=>useCustomerDetails,
    "useUpdateCustomer",
    ()=>useUpdateCustomer
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/constants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
;
;
;
const useCustomerDetails = function() {
    let enabled = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;
    _s();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('/customers/me/');
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CUSTOMER_DETAILS"]
        ],
        queryFn: {
            "useCustomerDetails.useQuery": ()=>apiClient.get()
        }["useCustomerDetails.useQuery"],
        enabled
    });
};
_s(useCustomerDetails, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useUpdateCustomer = ()=>{
    _s1();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("/customers/me/");
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useUpdateCustomer.useMutation[mutation]": (customerData)=>apiClient.patch(customerData)
        }["useUpdateCustomer.useMutation[mutation]"],
        onSuccess: {
            "useUpdateCustomer.useMutation[mutation]": ()=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CUSTOMER_DETAILS"]
                    ]
                });
            }
        }["useUpdateCustomer.useMutation[mutation]"]
    });
    return {
        mutation
    };
};
_s1(useUpdateCustomer, "mUUakYvFQSudGRmMzZErOI9nuuc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/hooks/wishlist-hooks.ts [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "useDeleteWishlistItem",
    ()=>useDeleteWishlistItem,
    "useToggleWishlist",
    ()=>useToggleWishlist,
    "useWishlist",
    ()=>useWishlist
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/constants.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
;
;
;
const useWishlist = function(page) {
    let enabled = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;
    _s();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("/wishlist/");
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WISHLIST_ITEMS"],
            page
        ],
        queryFn: {
            "useWishlist.useQuery": ()=>apiClient.getAll({
                    params: {
                        page: page
                    }
                })
        }["useWishlist.useQuery"],
        enabled,
        // keepPreviousData: true,
        // staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours
        // initialData:  Here we can add categories as static data
        // refetchOnMount: true,
        staleTime: 0
    });
};
_s(useWishlist, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useToggleWishlist = ()=>{
    _s1();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("/wishlist/toggle_product/");
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useToggleWishlist.useMutation[mutation]": (productId)=>apiClient.post({
                    "product_id": "".concat(productId)
                })
        }["useToggleWishlist.useMutation[mutation]"],
        // onMutate: async (itemId) => {
        //   await queryClient.cancelQueries(CACHE_KEY_CART_ITEMS)
        //   const previousCartItems = queryClient.getQueryData(CACHE_KEY_CART_ITEMS)
        //   queryClient.setQueryData(CACHE_KEY_CART_ITEMS, (oldData: CartShape) => {
        //     const updatedCartItems = oldData?.cart_items.filter(
        //       (item) => item.id !== itemId
        //     )
        //     return { ...oldData, cart_items: updatedCartItems }
        //   })
        //   return { previousCartItems }
        // },
        onSuccess: {
            "useToggleWishlist.useMutation[mutation]": ()=>{
                // queryClient.invalidateQueries(CACHE_KEY_CART_ITEMS)
                queryClient.invalidateQueries({
                    queryKey: [
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WISHLIST_ITEMS"]
                    ]
                });
            }
        }["useToggleWishlist.useMutation[mutation]"]
    });
    // const handleDeleteCartItem = (itemId) => {
    //   mutate({ itemId })
    // }
    // return { isPending, isError, mutate }
    return mutation;
};
_s1(useToggleWishlist, "mUUakYvFQSudGRmMzZErOI9nuuc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useDeleteWishlistItem = ()=>{
    _s2();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('/wishlist');
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useDeleteWishlistItem.useMutation[mutation]": (itemId)=>{
                // This will correctly construct the URL as /wishlist/123/
                return apiClient.delete(itemId);
            }
        }["useDeleteWishlistItem.useMutation[mutation]"],
        onSuccess: {
            "useDeleteWishlistItem.useMutation[mutation]": ()=>{
                // Invalidate the wishlist query to refetch the latest data
                queryClient.invalidateQueries({
                    queryKey: [
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WISHLIST_ITEMS"]
                    ]
                });
            }
        }["useDeleteWishlistItem.useMutation[mutation]"],
        onError: {
            "useDeleteWishlistItem.useMutation[mutation]": (error)=>{
                // Optional: Add error handling
                console.error('Failed to delete wishlist item:', error);
            }
        }["useDeleteWishlistItem.useMutation[mutation]"]
    });
    return mutation;
};
_s2(useDeleteWishlistItem, "mUUakYvFQSudGRmMzZErOI9nuuc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/utils/logo/Logo.module.scss [app-client] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "logo": "Logo-module-scss-module__VZ4g6W__logo",
});
}),
"[project]/src/components/utils/logo/Logo.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$logo$2f$Logo$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/utils/logo/Logo.module.scss [app-client] (css module)");
;
;
;
const Logo = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        href: "/",
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$logo$2f$Logo$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].logo,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                children: "PC"
            }, void 0, false, {
                fileName: "[project]/src/components/utils/logo/Logo.tsx",
                lineNumber: 9,
                columnNumber: 6
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                children: "House"
            }, void 0, false, {
                fileName: "[project]/src/components/utils/logo/Logo.tsx",
                lineNumber: 9,
                columnNumber: 17
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/utils/logo/Logo.tsx",
        lineNumber: 6,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_c = Logo;
const __TURBOPACK__default__export__ = Logo;
var _c;
__turbopack_context__.k.register(_c, "Logo");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/header/navbar/navigation-card/NavigationCard.module.scss [app-client] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "level-0": "NavigationCard-module-scss-module__8oXgDG__level-0",
  "level-1": "NavigationCard-module-scss-module__8oXgDG__level-1",
  "level-2": "NavigationCard-module-scss-module__8oXgDG__level-2",
  "level-3": "NavigationCard-module-scss-module__8oXgDG__level-3",
  "list": "NavigationCard-module-scss-module__8oXgDG__list",
  "nav__card": "NavigationCard-module-scss-module__8oXgDG__nav__card",
  "slideDown": "NavigationCard-module-scss-module__8oXgDG__slideDown",
});
}),
"[project]/src/components/utils/alert/Alert.module.scss [app-client] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "alert": "Alert-module-scss-module__QYFb8W__alert",
  "error": "Alert-module-scss-module__QYFb8W__error",
  "highlight": "Alert-module-scss-module__QYFb8W__highlight",
  "info": "Alert-module-scss-module__QYFb8W__info",
  "success": "Alert-module-scss-module__QYFb8W__success",
  "warning": "Alert-module-scss-module__QYFb8W__warning",
});
}),
"[project]/src/components/utils/alert/Alert.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$alert$2f$Alert$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/utils/alert/Alert.module.scss [app-client] (css module)");
;
;
const Alert = (param)=>{
    let { variant, message, textSize, textAlign = 'left', highlightWords = [] } = param;
    const escapeRegExp = (string)=>{
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') // Escape special characters
        ;
    };
    const getHighlightedMessage = (text)=>{
        if (!highlightWords.length) {
            return text;
        }
        // Escape each word in the highlightWords array
        const escapedHighlightWords = highlightWords.map((word)=>escapeRegExp(word));
        const parts = text.split(new RegExp("(".concat(escapedHighlightWords.join('|'), ")"), 'gi'));
        return parts.map((part, index)=>highlightWords.some((word)=>word.toLowerCase() === part.toLowerCase()) ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$alert$2f$Alert$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].highlight,
                children: part
            }, index, false, {
                fileName: "[project]/src/components/utils/alert/Alert.tsx",
                lineNumber: 28,
                columnNumber: 11
            }, ("TURBOPACK compile-time value", void 0)) : part);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "".concat(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$alert$2f$Alert$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"][variant], " ").concat(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$alert$2f$Alert$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].alert),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
            style: {
                fontSize: "".concat(textSize, "px"),
                textAlign: textAlign
            },
            children: getHighlightedMessage(message)
        }, void 0, false, {
            fileName: "[project]/src/components/utils/alert/Alert.tsx",
            lineNumber: 36,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/utils/alert/Alert.tsx",
        lineNumber: 35,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_c = Alert;
const __TURBOPACK__default__export__ = Alert;
var _c;
__turbopack_context__.k.register(_c, "Alert");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/utils/getErrorMessage.ts [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "getErrorMessage",
    ()=>getErrorMessage
]);
const getErrorMessage = (error)=>{
    var _error_response;
    if (error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) {
        return Object.values(error.response.data).flat().join(', ');
    }
    return error === null || error === void 0 ? void 0 : error.message;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/header/navbar/navigation-card/NavigationCard.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$navigation$2d$card$2f$NavigationCard$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/header/navbar/navigation-card/NavigationCard.module.scss [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$alert$2f$Alert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/utils/alert/Alert.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$getErrorMessage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/utils/getErrorMessage.ts [app-client] (ecmascript)");
;
;
;
;
;
// Utility function to build a hierarchical structure of categories
const buildHierarchy = (categories)=>{
    const categoryMap = new Map() // Map to hold categories by their IDs
    ;
    // Initialize the map with empty children arrays
    categories.forEach((cat)=>categoryMap.set(cat.id, {
            ...cat,
            children: []
        }));
    const rootCategories = [] // Array to hold top-level categories
    ;
    categories.forEach((cat)=>{
        if (cat.parent === null) {
            rootCategories.push(categoryMap.get(cat.id)); // Add top-level categories (parent is null) to rootCategories
        } else {
            const parentCategory = categoryMap.get(cat.parent);
            if (parentCategory) {
                parentCategory.children.push(categoryMap.get(cat.id)); // Add the current category to its parent's children array
            }
        }
    });
    return rootCategories // Return the hierarchical structure
    ;
};
// Main functional component for the navigation card
const NavigationCard = (param)=>{
    let { isPending, error, categories, setIsOpen } = param;
    var _this = ("TURBOPACK compile-time value", void 0);
    const hierarchicalCategories = buildHierarchy(categories) // Build hierarchical structure of categories
    ;
    // Recursive function to render categories and their children
    const renderCategories = function(items) {
        let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
            className: "".concat(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$navigation$2d$card$2f$NavigationCard$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].list, " ").concat(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$navigation$2d$card$2f$NavigationCard$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"]["level-".concat(level)]),
            children: items.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            href: "/products/category/".concat(item.slug),
                            onClick: ()=>setIsOpen(false),
                            children: [
                                item.title,
                                " "
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/header/navbar/navigation-card/NavigationCard.tsx",
                            lineNumber: 53,
                            columnNumber: 11
                        }, _this),
                        item.children && item.children.length > 0 && renderCategories(item.children, level + 1)
                    ]
                }, item.id, true, {
                    fileName: "[project]/src/components/header/navbar/navigation-card/NavigationCard.tsx",
                    lineNumber: 52,
                    columnNumber: 9
                }, _this))
        }, void 0, false, {
            fileName: "[project]/src/components/header/navbar/navigation-card/NavigationCard.tsx",
            lineNumber: 49,
            columnNumber: 5
        }, _this);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        onMouseLeave: ()=>setIsOpen(false),
        onMouseOver: ()=>setIsOpen(true),
        className: "".concat(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$navigation$2d$card$2f$NavigationCard$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].nav__card, " container"),
        children: isPending ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: "Loading..."
        }, void 0, false, {
            fileName: "[project]/src/components/header/navbar/navigation-card/NavigationCard.tsx",
            lineNumber: 73,
            columnNumber: 9
        }, ("TURBOPACK compile-time value", void 0)) : error ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$alert$2f$Alert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            variant: "error",
            message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$getErrorMessage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getErrorMessage"])(error)
        }, void 0, false, {
            fileName: "[project]/src/components/header/navbar/navigation-card/NavigationCard.tsx",
            lineNumber: 75,
            columnNumber: 9
        }, ("TURBOPACK compile-time value", void 0)) : renderCategories(hierarchicalCategories)
    }, void 0, false, {
        fileName: "[project]/src/components/header/navbar/navigation-card/NavigationCard.tsx",
        lineNumber: 67,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_c = NavigationCard;
const __TURBOPACK__default__export__ = NavigationCard;
var _c;
__turbopack_context__.k.register(_c, "NavigationCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/stores/filter-store.ts [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

// Zustand store (filterStore.ts)
__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
;
;
const filterStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        productTypeId: 0,
        currentCategory: '',
        categoryProductTypeMap: {},
        selectedFilters: {},
        setProductTypeId: (proType)=>set({
                productTypeId: proType
            }),
        setCurrentCategory: (category)=>{
            const state = get();
            if (state.currentCategory !== category) {
                // Category changed, reset filters and update current category
                const cachedProductTypeId = state.categoryProductTypeMap[category];
                set({
                    currentCategory: category,
                    selectedFilters: {},
                    // Only set productTypeId if we have cached data, otherwise keep current value
                    ...cachedProductTypeId && {
                        productTypeId: cachedProductTypeId
                    }
                });
            }
        },
        setCategoryProductType: (category, productTypeId)=>{
            const state = get();
            // Only update if the productTypeId is different to avoid unnecessary re-renders
            if (state.categoryProductTypeMap[category] !== productTypeId || state.productTypeId !== productTypeId) {
                set({
                    categoryProductTypeMap: {
                        ...state.categoryProductTypeMap,
                        [category]: productTypeId
                    },
                    productTypeId: productTypeId
                });
            }
        },
        updateFilter: (filterName, filterValue)=>set((state)=>({
                    selectedFilters: {
                        ...state.selectedFilters,
                        [filterName]: filterValue
                    }
                })),
        resetFilters: ()=>set({
                selectedFilters: {}
            }),
        shouldFetchFilters: (category)=>{
            const state = get();
            // Only fetch filters if we don't have cached productTypeId for this category
            return !state.categoryProductTypeMap[category];
        }
    }), {
    name: 'filter-store',
    partialize: (state)=>({
            categoryProductTypeMap: state.categoryProductTypeMap
        })
}));
const __TURBOPACK__default__export__ = filterStore;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/hooks/product-hooks.ts [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "useCategories",
    ()=>useCategories,
    "useProductFilterOptions",
    ()=>useProductFilterOptions,
    "useProducts",
    ()=>useProducts
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/constants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$filter$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/filter-store.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
;
;
;
;
const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('/products/categories/');
const useCategories = ()=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            'categories'
        ],
        queryFn: {
            "useCategories.useQuery": (param)=>{
                let { signal } = param;
                return apiClient.get({
                    signal
                });
            }
        }["useCategories.useQuery"],
        staleTime: 24 * 60 * 60 * 1000,
        // initialData:  Here we can add categories as static data
        // cacheTime: 1000 * 60 * 60, <-- This is wrong!
        gcTime: 1000 * 60 * 60,
        retry: {
            "useCategories.useQuery": (failureCount, error)=>{
                // ✅ Don't retry on cancelled requests
                if (error.name === 'CanceledError' || error.message === 'canceled') {
                    return false;
                }
                return failureCount < 3;
            }
        }["useCategories.useQuery"]
    });
};
_s(useCategories, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useProducts = function(slug, page, queryString) {
    let searchQuery = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : '';
    _s1();
    const { selectedFilters } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$filter$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    // Initialize API client based on whether searchQuery exists or not
    const apiClient = searchQuery ? new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("/products/?search=".concat(searchQuery)) : new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("/products/category/".concat(slug, "/"));
    const queryParams = new URLSearchParams(queryString);
    // Add filters to the query parameters
    Object.entries(selectedFilters).forEach((param)=>{
        let [key, value] = param;
        if (key === 'price_range' && Array.isArray(value)) {
            queryParams.set('min_price', value[0].toString());
            queryParams.set('max_price', value[1].toString());
        } else if (Array.isArray(value)) {
            value.forEach((val)=>queryParams.append(key, val.toString()));
        } else {
            queryParams.set(key, value.toString());
        }
    });
    // If searchQuery exists, add it to queryParams
    if (searchQuery) {
        queryParams.set('search', searchQuery);
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_KEY_PRODUCTS"],
            slug,
            selectedFilters,
            page,
            queryString,
            searchQuery
        ],
        queryFn: {
            "useProducts.useQuery": ()=>apiClient.getAll({
                    params: Object.fromEntries(queryParams)
                })
        }["useProducts.useQuery"]
    });
};
_s1(useProducts, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useProductFilterOptions = (productTypeId)=>{
    _s2();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('/products/product-filter-options/');
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            'filterOptions',
            productTypeId
        ],
        queryFn: {
            "useProductFilterOptions.useQuery": ()=>apiClient.get({
                    params: {
                        product_type_id: productTypeId
                    }
                })
        }["useProductFilterOptions.useQuery"],
        enabled: !!productTypeId && productTypeId > 0,
        staleTime: 1000 * 60 * 60 * 24,
        gcTime: 1000 * 60 * 60 * 24,
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        retry: 1
    });
};
_s2(useProductFilterOptions, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/header/navbar/Navbar.module.scss [app-client] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "navbar__links": "Navbar-module-scss-module__deXsXG__navbar__links",
});
}),
"[project]/src/components/header/navbar/Navbar.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa6$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fa6/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$navigation$2d$card$2f$NavigationCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/header/navbar/navigation-card/NavigationCard.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$product$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/product-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$Navbar$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/header/navbar/Navbar.module.scss [app-client] (css module)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
const Navbar = ()=>{
    _s();
    const { isPending, error, data: categories = [] } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$product$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCategories"])();
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$Navbar$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].navbar,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$Navbar$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].navbar__links,
                    onMouseOver: ()=>setIsOpen(true),
                    onMouseLeave: ()=>setIsOpen(false),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            children: "All Products"
                        }, void 0, false, {
                            fileName: "[project]/src/components/header/navbar/Navbar.tsx",
                            lineNumber: 20,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa6$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaAnglesDown"], {}, void 0, false, {
                                fileName: "[project]/src/components/header/navbar/Navbar.tsx",
                                lineNumber: 21,
                                columnNumber: 14
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/header/navbar/Navbar.tsx",
                            lineNumber: 21,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/header/navbar/Navbar.tsx",
                    lineNumber: 15,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/header/navbar/Navbar.tsx",
                lineNumber: 14,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            isOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$navigation$2d$card$2f$NavigationCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                isPending: isPending,
                error: error,
                categories: categories,
                isOpen: isOpen,
                setIsOpen: setIsOpen
            }, void 0, false, {
                fileName: "[project]/src/components/header/navbar/Navbar.tsx",
                lineNumber: 25,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true);
};
_s(Navbar, "ZcTca1aj9YAH1MmxSelKYWbZj/8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$product$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCategories"]
    ];
});
_c = Navbar;
const __TURBOPACK__default__export__ = Navbar;
var _c;
__turbopack_context__.k.register(_c, "Navbar");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/header/search-bar/Search.module.scss [app-client] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "backdrop": "Search-module-scss-module__pWWbXW__backdrop",
  "category_header": "Search-module-scss-module__pWWbXW__category_header",
  "category_item": "Search-module-scss-module__pWWbXW__category_item",
  "category_link": "Search-module-scss-module__pWWbXW__category_link",
  "category_title": "Search-module-scss-module__pWWbXW__category_title",
  "child_categories": "Search-module-scss-module__pWWbXW__child_categories",
  "expand_toggle": "Search-module-scss-module__pWWbXW__expand_toggle",
  "expandable": "Search-module-scss-module__pWWbXW__expandable",
  "expanded": "Search-module-scss-module__pWWbXW__expanded",
  "focused": "Search-module-scss-module__pWWbXW__focused",
  "leaf_category": "Search-module-scss-module__pWWbXW__leaf_category",
  "level_0": "Search-module-scss-module__pWWbXW__level_0",
  "level_1": "Search-module-scss-module__pWWbXW__level_1",
  "level_2": "Search-module-scss-module__pWWbXW__level_2",
  "level_3": "Search-module-scss-module__pWWbXW__level_3",
  "no_suggestions": "Search-module-scss-module__pWWbXW__no_suggestions",
  "parent_category": "Search-module-scss-module__pWWbXW__parent_category",
  "product_count": "Search-module-scss-module__pWWbXW__product_count",
  "search": "Search-module-scss-module__pWWbXW__search",
  "search_suggestions": "Search-module-scss-module__pWWbXW__search_suggestions",
  "slideIn": "Search-module-scss-module__pWWbXW__slideIn",
  "suggestions": "Search-module-scss-module__pWWbXW__suggestions",
});
}),
"[project]/src/components/header/search-bar/Search.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/io/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/header/search-bar/Search.module.scss [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$product$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/product-hooks.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
// Utility function to calculate category depth in hierarchy
const calculateCategoryDepth = function(categoryId, allCategories) {
    let currentDepth = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;
    const category = allCategories.find((cat)=>cat.id === categoryId);
    if (!category || !category.parent) {
        return currentDepth;
    }
    return calculateCategoryDepth(category.parent, allCategories, currentDepth + 1);
};
// Utility function to enhance categories with new properties
const enhanceCategory = (category, allCategories)=>{
    const children = allCategories.filter((cat)=>cat.parent === category.id);
    const hasChildren = children.length > 0;
    const depth = calculateCategoryDepth(category.id, allCategories);
    return {
        ...category,
        hasChildren,
        depth,
        children: hasChildren ? children.map((child)=>enhanceCategory(child, allCategories)) : undefined
    };
};
const Search = ()=>{
    _s();
    // Simplified state management
    const [searchValue, setSearchValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [showSuggestions, setShowSuggestions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [suggestedCategories, setSuggestedCategories] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    // Navigation hook to programmatically navigate to different pages
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    // Ref to the search container to detect clicks outside of it
    const searchRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Fetch all categories using the custom hook `useCategories`
    const { data: categories = [] } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$product$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCategories"])();
    // Enhanced recursive function to find all children for a given category
    const findAllChildrenRecursive = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "Search.useCallback[findAllChildrenRecursive]": (categoryId, allCategories)=>{
            // Find all categories whose parent matches the given category ID
            const children = allCategories.filter({
                "Search.useCallback[findAllChildrenRecursive].children": (cat)=>cat.parent === categoryId
            }["Search.useCallback[findAllChildrenRecursive].children"]);
            // For each child, recursively find its own children and enhance them
            return children.map({
                "Search.useCallback[findAllChildrenRecursive]": (child)=>{
                    const enhancedChild = enhanceCategory(child, allCategories);
                    return {
                        ...enhancedChild,
                        children: findAllChildrenRecursive(child.id, allCategories)
                    };
                }
            }["Search.useCallback[findAllChildrenRecursive]"]);
        }
    }["Search.useCallback[findAllChildrenRecursive]"], []);
    // useEffect hook to update the suggestions list based on the search input
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Search.useEffect": ()=>{
            if (searchValue.trim()) {
                const lowerCaseSearchValue = searchValue.toLowerCase();
                // Filter categories based on the search value (case insensitive)
                const matchedCategories = categories.filter({
                    "Search.useEffect.matchedCategories": (category)=>category.title.toLowerCase().includes(lowerCaseSearchValue)
                }["Search.useEffect.matchedCategories"]);
                // For each matched category, enhance it and find its children recursively
                const enhancedCategoriesWithChildren = matchedCategories.map({
                    "Search.useEffect.enhancedCategoriesWithChildren": (category)=>{
                        const enhanced = enhanceCategory(category, categories);
                        return {
                            ...enhanced,
                            children: findAllChildrenRecursive(category.id, categories),
                            matchScore: lowerCaseSearchValue === category.title.toLowerCase() ? 100 : category.title.toLowerCase().startsWith(lowerCaseSearchValue) ? 80 : 50
                        };
                    }
                }["Search.useEffect.enhancedCategoriesWithChildren"]);
                // Sort by match score for better relevance
                const sortedCategories = enhancedCategoriesWithChildren.sort({
                    "Search.useEffect.sortedCategories": (a, b)=>(b.matchScore || 0) - (a.matchScore || 0)
                }["Search.useEffect.sortedCategories"]);
                setSuggestedCategories(sortedCategories);
            } else {
                setSuggestedCategories([]);
            }
        }
    }["Search.useEffect"], [
        searchValue,
        categories,
        findAllChildrenRecursive
    ]);
    // useEffect hook to handle clicks outside the search component
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Search.useEffect": ()=>{
            const handleClickOutside = {
                "Search.useEffect.handleClickOutside": (event)=>{
                    // If the click is outside the search box, close the suggestions dropdown
                    if (searchRef.current && !searchRef.current.contains(event.target)) {
                        setShowSuggestions(false);
                    }
                }
            }["Search.useEffect.handleClickOutside"];
            // Add event listener for mouse clicks
            document.addEventListener('mousedown', handleClickOutside);
            // Cleanup the event listener when the component is unmounted
            return ({
                "Search.useEffect": ()=>{
                    document.removeEventListener('mousedown', handleClickOutside);
                }
            })["Search.useEffect"];
        }
    }["Search.useEffect"], []);
    // Handler for form submission (search submit)
    const handleSearchSubmit = (event)=>{
        event.preventDefault();
        // If the search value is valid (non-empty), navigate to the search results page
        if (searchValue.trim()) {
            router.push("/products/?search=".concat(searchValue));
            // Close the suggestions dropdown
            setShowSuggestions(false);
        }
    };
    // Enhanced render function for categories with proper structure
    const renderCategories = (category)=>{
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "".concat(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].category_item, " ").concat(category.hasChildren ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].expandable : ''),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].category_header,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        href: "/products/category/".concat(category.slug),
                        className: "".concat(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].category_link, " ").concat(category.hasChildren ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].parent_category : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].leaf_category),
                        onClick: ()=>setShowSuggestions(false),
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "".concat(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].category_title, " ").concat(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"]["level_".concat(Math.min(category.depth, 3))]),
                            children: category.title
                        }, void 0, false, {
                            fileName: "[project]/src/components/header/search-bar/Search.tsx",
                            lineNumber: 161,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/header/search-bar/Search.tsx",
                        lineNumber: 156,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/header/search-bar/Search.tsx",
                    lineNumber: 154,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                category.children && category.children.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].child_categories,
                    children: category.children.map((child)=>renderCategories(child))
                }, void 0, false, {
                    fileName: "[project]/src/components/header/search-bar/Search.tsx",
                    lineNumber: 171,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, category.id, true, {
            fileName: "[project]/src/components/header/search-bar/Search.tsx",
            lineNumber: 151,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0));
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].search,
        ref: searchRef,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                onSubmit: handleSearchSubmit,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        type: "text",
                        placeholder: "Search...",
                        value: searchValue,
                        onChange: (e)=>setSearchValue(e.target.value),
                        onFocus: ()=>setShowSuggestions(true),
                        autoComplete: "off"
                    }, void 0, false, {
                        fileName: "[project]/src/components/header/search-bar/Search.tsx",
                        lineNumber: 183,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "submit",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IoMdSearch"], {}, void 0, false, {
                                fileName: "[project]/src/components/header/search-bar/Search.tsx",
                                lineNumber: 192,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            "  "
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/header/search-bar/Search.tsx",
                        lineNumber: 191,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/header/search-bar/Search.tsx",
                lineNumber: 182,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            showSuggestions && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].search_suggestions,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].backdrop,
                        onClick: ()=>setShowSuggestions(false)
                    }, void 0, false, {
                        fileName: "[project]/src/components/header/search-bar/Search.tsx",
                        lineNumber: 199,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    "  ",
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].suggestions,
                        children: suggestedCategories.length > 0 ? suggestedCategories.map((category)=>renderCategories(category)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].no_suggestions,
                            children: "No suggestions found"
                        }, void 0, false, {
                            fileName: "[project]/src/components/header/search-bar/Search.tsx",
                            lineNumber: 206,
                            columnNumber: 15
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/header/search-bar/Search.tsx",
                        lineNumber: 201,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/header/search-bar/Search.tsx",
                lineNumber: 198,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/header/search-bar/Search.tsx",
        lineNumber: 180,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(Search, "//of0izSQcY/BMcl6WPaDLIPkU0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$product$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCategories"]
    ];
});
_c = Search;
const __TURBOPACK__default__export__ = Search;
var _c;
__turbopack_context__.k.register(_c, "Search");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/header/Header.module.scss [app-client] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "active": "Header-module-scss-module__yUQ6Nq__active",
  "cart": "Header-module-scss-module__yUQ6Nq__cart",
  "divider": "Header-module-scss-module__yUQ6Nq__divider",
  "dropdown": "Header-module-scss-module__yUQ6Nq__dropdown",
  "dropdown_container": "Header-module-scss-module__yUQ6Nq__dropdown_container",
  "dropdown_footer": "Header-module-scss-module__yUQ6Nq__dropdown_footer",
  "dropdown_header": "Header-module-scss-module__yUQ6Nq__dropdown_header",
  "dropdown_menu": "Header-module-scss-module__yUQ6Nq__dropdown_menu",
  "header": "Header-module-scss-module__yUQ6Nq__header",
  "header__badge": "Header-module-scss-module__yUQ6Nq__header__badge",
  "header__bottom_nav": "Header-module-scss-module__yUQ6Nq__header__bottom_nav",
  "header__end": "Header-module-scss-module__yUQ6Nq__header__end",
  "header__icon": "Header-module-scss-module__yUQ6Nq__header__icon",
  "header__login": "Header-module-scss-module__yUQ6Nq__header__login",
  "header__login_links": "Header-module-scss-module__yUQ6Nq__header__login_links",
  "header__logo": "Header-module-scss-module__yUQ6Nq__header__logo",
  "header__search": "Header-module-scss-module__yUQ6Nq__header__search",
  "header__sign_in": "Header-module-scss-module__yUQ6Nq__header__sign_in",
  "header__top": "Header-module-scss-module__yUQ6Nq__header__top",
  "menu_item": "Header-module-scss-module__yUQ6Nq__menu_item",
  "warning_link": "Header-module-scss-module__yUQ6Nq__warning_link",
  "wishlist": "Header-module-scss-module__yUQ6Nq__wishlist",
});
}),
"[project]/src/components/header/Header.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/auth-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/cart-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$customer$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/customer-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$wishlist$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/wishlist-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/auth-store.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$bs$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/bs/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fa/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/md/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$ri$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/ri/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$logo$2f$Logo$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/utils/logo/Logo.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$Navbar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/header/navbar/Navbar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/header/search-bar/Search.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/header/Header.module.scss [app-client] (css module)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const Header = ()=>{
    _s();
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const { data } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCart"])();
    const { isLoggedIn } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const { mutation } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLogout"])();
    const { data: wishlistItems } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$wishlist$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useWishlist"])(1, isLoggedIn);
    const { data: customerData } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$customer$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCustomerDetails"])(isLoggedIn);
    const handleLinkClick = (event)=>{
        if (event.target.tagName === "A") {
            setIsOpen(false);
        }
    };
    const handleLogout = ()=>{
        mutation.mutate();
    };
    const cartItemsQuantity = (cart_items)=>{
        let item_qty = 0;
        if (cart_items) {
            cart_items.forEach((item)=>{
                item_qty += item.quantity;
            });
        }
        return item_qty;
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Header.useEffect": ()=>{}
    }["Header.useEffect"], [
        customerData,
        isLoggedIn
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].header,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                    className: "".concat(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].header__top, " container"),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].header__logo,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$logo$2f$Logo$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                fileName: "[project]/src/components/header/Header.tsx",
                                lineNumber: 58,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/header/Header.tsx",
                            lineNumber: 57,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].header__search,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                fileName: "[project]/src/components/header/Header.tsx",
                                lineNumber: 63,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/header/Header.tsx",
                            lineNumber: 62,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].header__end,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/account/wishlist",
                                    title: "wishlist",
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].wishlist,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].header__badge,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: (wishlistItems === null || wishlistItems === void 0 ? void 0 : wishlistItems.count) || 0
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/header/Header.tsx",
                                                lineNumber: 70,
                                                columnNumber: 17
                                            }, ("TURBOPACK compile-time value", void 0))
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/header/Header.tsx",
                                            lineNumber: 69,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].header__icon,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaHeart"], {}, void 0, false, {
                                                fileName: "[project]/src/components/header/Header.tsx",
                                                lineNumber: 72,
                                                columnNumber: 50
                                            }, ("TURBOPACK compile-time value", void 0))
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/header/Header.tsx",
                                            lineNumber: 72,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/header/Header.tsx",
                                    lineNumber: 68,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/checkout/cart",
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cart,
                                    title: "cart",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].header__badge,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: cartItemsQuantity((data === null || data === void 0 ? void 0 : data.cart_items) || [])
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/header/Header.tsx",
                                                lineNumber: 77,
                                                columnNumber: 51
                                            }, ("TURBOPACK compile-time value", void 0))
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/header/Header.tsx",
                                            lineNumber: 77,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].header__icon,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaCartPlus"], {}, void 0, false, {
                                                fileName: "[project]/src/components/header/Header.tsx",
                                                lineNumber: 78,
                                                columnNumber: 50
                                            }, ("TURBOPACK compile-time value", void 0))
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/header/Header.tsx",
                                            lineNumber: 78,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/header/Header.tsx",
                                    lineNumber: 76,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].header__sign_in,
                                    onMouseEnter: ()=>isLoggedIn && setIsOpen(true),
                                    onMouseLeave: ()=>isLoggedIn && setIsOpen(false),
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "".concat(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].header__login, " ").concat(!isLoggedIn ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].header__login_links : ''),
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        !isLoggedIn && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                    href: "/login/",
                                                                    children: "Sign In"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                                    lineNumber: 93,
                                                                    columnNumber: 23
                                                                }, ("TURBOPACK compile-time value", void 0)),
                                                                " | ",
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                    href: "/register/initiate/",
                                                                    children: "Sign Up"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                                    lineNumber: 95,
                                                                    columnNumber: 23
                                                                }, ("TURBOPACK compile-time value", void 0))
                                                            ]
                                                        }, void 0, true),
                                                        isLoggedIn && (customerData === null || customerData === void 0 ? void 0 : customerData.first_name) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                "Hello, ",
                                                                customerData.first_name
                                                            ]
                                                        }, void 0, true),
                                                        isLoggedIn && !(customerData === null || customerData === void 0 ? void 0 : customerData.first_name) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].warning_link,
                                                            href: "/account/profile",
                                                            children: "!Complete your profile"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                            lineNumber: 102,
                                                            columnNumber: 21
                                                        }, ("TURBOPACK compile-time value", void 0))
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                    lineNumber: 90,
                                                    columnNumber: 17
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                isLoggedIn && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaCaretDown"], {}, void 0, false, {
                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                        lineNumber: 105,
                                                        columnNumber: 35
                                                    }, ("TURBOPACK compile-time value", void 0))
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                    lineNumber: 105,
                                                    columnNumber: 32
                                                }, ("TURBOPACK compile-time value", void 0))
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/header/Header.tsx",
                                            lineNumber: 87,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        isOpen && isLoggedIn && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "".concat(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].dropdown_container, " ").concat(isOpen ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].active : ''),
                                            onClick: handleLinkClick,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].dropdown,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].dropdown_header,
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                            children: [
                                                                "Welcome back, ",
                                                                (customerData === null || customerData === void 0 ? void 0 : customerData.first_name) || 'User'
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                            lineNumber: 114,
                                                            columnNumber: 23
                                                        }, ("TURBOPACK compile-time value", void 0))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                        lineNumber: 113,
                                                        columnNumber: 21
                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].dropdown_menu,
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menu_item,
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MdOutlineAccountCircle"], {}, void 0, false, {
                                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                                            lineNumber: 118,
                                                                            columnNumber: 28
                                                                        }, ("TURBOPACK compile-time value", void 0))
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                                        lineNumber: 118,
                                                                        columnNumber: 25
                                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                        href: "/account/profile",
                                                                        children: "My Account"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                                        lineNumber: 119,
                                                                        columnNumber: 25
                                                                    }, ("TURBOPACK compile-time value", void 0))
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/header/Header.tsx",
                                                                lineNumber: 117,
                                                                columnNumber: 23
                                                            }, ("TURBOPACK compile-time value", void 0)),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menu_item,
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$bs$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BsBox2"], {}, void 0, false, {
                                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                                            lineNumber: 122,
                                                                            columnNumber: 28
                                                                        }, ("TURBOPACK compile-time value", void 0))
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                                        lineNumber: 122,
                                                                        columnNumber: 25
                                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                        href: "/account/orders",
                                                                        children: "My Orders"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                                        lineNumber: 123,
                                                                        columnNumber: 25
                                                                    }, ("TURBOPACK compile-time value", void 0))
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/header/Header.tsx",
                                                                lineNumber: 121,
                                                                columnNumber: 23
                                                            }, ("TURBOPACK compile-time value", void 0)),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menu_item,
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$bs$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BsBox2"], {}, void 0, false, {
                                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                                            lineNumber: 126,
                                                                            columnNumber: 28
                                                                        }, ("TURBOPACK compile-time value", void 0))
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                                        lineNumber: 126,
                                                                        columnNumber: 25
                                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                        href: "/account/wishlist",
                                                                        children: "My WishList"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                                        lineNumber: 127,
                                                                        columnNumber: 25
                                                                    }, ("TURBOPACK compile-time value", void 0))
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/header/Header.tsx",
                                                                lineNumber: 125,
                                                                columnNumber: 23
                                                            }, ("TURBOPACK compile-time value", void 0))
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                        lineNumber: 116,
                                                        columnNumber: 21
                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].dropdown_footer,
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menu_item,
                                                            onClick: handleLogout,
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$ri$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RiLogoutCircleRLine"], {}, void 0, false, {
                                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                                        lineNumber: 137,
                                                                        columnNumber: 28
                                                                    }, ("TURBOPACK compile-time value", void 0))
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                                    lineNumber: 137,
                                                                    columnNumber: 25
                                                                }, ("TURBOPACK compile-time value", void 0)),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    children: "Sign Out"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                                    lineNumber: 138,
                                                                    columnNumber: 25
                                                                }, ("TURBOPACK compile-time value", void 0))
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                            lineNumber: 136,
                                                            columnNumber: 23
                                                        }, ("TURBOPACK compile-time value", void 0))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                        lineNumber: 135,
                                                        columnNumber: 21
                                                    }, ("TURBOPACK compile-time value", void 0))
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/header/Header.tsx",
                                                lineNumber: 112,
                                                columnNumber: 19
                                            }, ("TURBOPACK compile-time value", void 0))
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/header/Header.tsx",
                                            lineNumber: 108,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/header/Header.tsx",
                                    lineNumber: 82,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/header/Header.tsx",
                            lineNumber: 67,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/header/Header.tsx",
                    lineNumber: 55,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/header/Header.tsx",
                lineNumber: 54,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].header__bottom_nav,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                    className: "".concat(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].header__bottom, " container"),
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$Navbar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                        fileName: "[project]/src/components/header/Header.tsx",
                        lineNumber: 150,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/header/Header.tsx",
                    lineNumber: 149,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/header/Header.tsx",
                lineNumber: 148,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true);
};
_s(Header, "VmJGYfk6Gyhif7EVHzrI6oeYFIM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCart"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLogout"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$wishlist$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useWishlist"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$customer$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCustomerDetails"]
    ];
});
_c = Header;
const __TURBOPACK__default__export__ = Header;
var _c;
__turbopack_context__.k.register(_c, "Header");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
]);

//# sourceMappingURL=src_411afcd1._.js.map